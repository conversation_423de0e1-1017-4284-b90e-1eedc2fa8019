package backend

import (
	"context"
	"desktop/backend/dto"
	"desktop/backend/models"
	"desktop/backend/rclone"
	"desktop/backend/utils"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	fsConfig "github.com/rclone/rclone/fs/config"
	"github.com/rclone/rclone/fs/rc"
	"github.com/rclone/rclone/lib/oauthutil"
	"github.com/wailsapp/wails/v2/pkg/runtime"
)

func (a *App) Sync(task string, profile models.Profile) int {
	return a.SyncWithTab(task, profile, "")
}

func (a *App) SyncWithTab(task string, profile models.Profile, tabId string) int {
	id := time.Now().Nanosecond()

	ctx, cancel := context.WithCancel(context.Background())

	config := a.ConfigInfo.EnvConfig

	ctx, err := rclone.InitConfig(ctx, config.DebugMode)
	if utils.HandleError(err, "", nil, nil) != nil {
		var j []byte
		if tabId != "" {
			j, _ = utils.NewCommandErrorDTOWithTab(id, err, tabId).ToJSON()
		} else {
			j, _ = utils.NewCommandErrorDTO(id, err).ToJSON()
		}
		a.oc <- j
		cancel()
		return 0
	}

	outLog := make(chan string)
	utils.AddCmd(id, func() {
		close(outLog)
		cancel()
	})

	if tabId != "" {
		utils.AddTabMapping(id, tabId)
	}

	go func() {
		for {
			logEntry, ok := <-outLog
			if !ok { // channel is closed
				break
			}
			if config.DebugMode {
				fmt.Println("--------------------")
				fmt.Println(logEntry)
			}
			var j []byte
			if tabId != "" {
				j, _ = utils.NewCommandOutputDTOWithTab(id, logEntry, tabId).ToJSON()
			} else {
				j, _ = utils.NewCommandOutputDTO(id, logEntry).ToJSON()
			}
			a.oc <- j
		}
	}()

	go func() {
		switch task {
		case "pull":
			err = rclone.Sync(ctx, config, "pull", profile, outLog)
		case "push":
			err = rclone.Sync(ctx, config, "push", profile, outLog)
		case "bi":
			err = rclone.BiSync(ctx, config, profile, false, outLog)
		case "bi-resync":
			err = rclone.BiSync(ctx, config, profile, true, outLog)
		}

		if utils.HandleError(err, "", nil, nil) != nil {
			var j []byte
			if tabId != "" {
				j, _ = utils.NewCommandErrorDTOWithTab(0, err, tabId).ToJSON()
			} else {
				j, _ = utils.NewCommandErrorDTO(0, err).ToJSON()
			}
			a.oc <- j
		}

		var j []byte
		if tabId != "" {
			j, _ = utils.NewCommandStoppedDTOWithTab(id, tabId).ToJSON()
			utils.RemoveTabMapping(id)
		} else {
			j, _ = utils.NewCommandStoppedDTO(id).ToJSON()
		}
		a.oc <- j

		log.Println("Sync stopped!")
	}()

	return id
}

func (a *App) StopCommand(id int) {
	cancel, exists := utils.GetCmd(id)
	if !exists {
		tabId, hasTab := utils.GetTabMapping(id)
		var j []byte
		if hasTab {
			j, _ = utils.NewCommandErrorDTOWithTab(id, errors.New("command not found"), tabId).ToJSON()
		} else {
			j, _ = utils.NewCommandErrorDTO(id, errors.New("command not found")).ToJSON()
		}
		a.oc <- j
		return
	}

	cancel()

	tabId, hasTab := utils.GetTabMapping(id)
	var res []byte
	if hasTab {
		res, _ = utils.NewCommandStoppedDTOWithTab(id, tabId).ToJSON()
		utils.RemoveTabMapping(id)
	} else {
		res, _ = utils.NewCommandStoppedDTO(id).ToJSON()
	}
	a.oc <- res
}

func (a *App) GetConfigInfo() models.ConfigInfo {
	return a.ConfigInfo
}

func (a *App) UpdateProfiles(profiles models.Profiles) *dto.AppError {
	a.ConfigInfo.Profiles = profiles

	profilesJson, err := profiles.ToJSON()
	if err != nil {
		return dto.NewAppError(err)
	}

	err = os.WriteFile(a.ConfigInfo.EnvConfig.ProfileFilePath, profilesJson, 0644)
	if err != nil {
		return dto.NewAppError(err)
	}

	return nil
}

func (a *App) GetRemotes() []fsConfig.Remote {
	return fsConfig.GetRemotes()
}

func (a *App) AddRemote(remoteName string, remoteType string, remoteConfig map[string]string) *dto.AppError {
	ctx := context.Background()
	_, err := fsConfig.CreateRemote(ctx, remoteName, remoteType, rc.Params{}, fsConfig.UpdateRemoteOpt{})
	if err != nil {
		a.DeleteRemote(remoteName)
	}

	return dto.NewAppError(err)
}

func (a *App) StopAddingRemote() *dto.AppError {
	const OAUTH_REDIRECT_URL = oauthutil.RedirectURL
	resp, err := http.Get(OAUTH_REDIRECT_URL)
	if err != nil {
		return dto.NewAppError(err)
	}

	defer resp.Body.Close()
	return nil
}

func (a *App) DeleteRemote(remoteName string) {
	fsConfig.DeleteRemote(remoteName)
}

func (a *App) SyncWithTabId(task string, profile models.Profile, tabId string) int {
	return a.SyncWithTab(task, profile, tabId)
}

// File dialog functions
func (a *App) OpenFileDialog(title string, filters []string) (string, *dto.AppError) {
	filePath, err := runtime.OpenFileDialog(a.ctx, runtime.OpenDialogOptions{
		Title: title,
		Filters: []runtime.FileFilter{
			{
				DisplayName: "JSON Files",
				Pattern:     "*.json",
			},
		},
	})
	if err != nil {
		return "", dto.NewAppError(err)
	}
	return filePath, nil
}

func (a *App) SaveFileDialog(title string, defaultFilename string, filters []string) (string, *dto.AppError) {
	filePath, err := runtime.SaveFileDialog(a.ctx, runtime.SaveDialogOptions{
		Title:           title,
		DefaultFilename: defaultFilename,
		Filters: []runtime.FileFilter{
			{
				DisplayName: "JSON Files",
				Pattern:     "*.json",
			},
		},
	})
	if err != nil {
		return "", dto.NewAppError(err)
	}
	return filePath, nil
}

// Profile import/export functions
func (a *App) ExportProfiles() *dto.AppError {
	filePath, appErr := a.SaveFileDialog("Export Profiles", "profiles.json", []string{"*.json"})
	if appErr != nil {
		return appErr
	}

	if filePath == "" {
		// User cancelled the dialog
		return nil
	}

	profilesJson, err := a.ConfigInfo.Profiles.ToJSON()
	if err != nil {
		return dto.NewAppError(err)
	}

	err = os.WriteFile(filePath, profilesJson, 0644)
	if err != nil {
		return dto.NewAppError(err)
	}

	return nil
}

func (a *App) ImportProfiles() *dto.AppError {
	filePath, appErr := a.OpenFileDialog("Import Profiles", []string{"*.json"})
	if appErr != nil {
		return appErr
	}

	if filePath == "" {
		// User cancelled the dialog
		return nil
	}

	// Read the file
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return dto.NewAppError(fmt.Errorf("failed to read file: %w", err))
	}

	// Parse the JSON
	var importedProfiles models.Profiles
	err = json.Unmarshal(fileData, &importedProfiles)
	if err != nil {
		return dto.NewAppError(fmt.Errorf("invalid JSON format: %w", err))
	}

	// Validate profiles
	for i, profile := range importedProfiles {
		if profile.Name == "" {
			return dto.NewAppError(fmt.Errorf("profile %d has no name", i+1))
		}
		if profile.From == "" {
			return dto.NewAppError(fmt.Errorf("profile '%s' has no source path", profile.Name))
		}
		if profile.To == "" {
			return dto.NewAppError(fmt.Errorf("profile '%s' has no destination path", profile.Name))
		}
	}

	// Merge with existing profiles (append imported profiles)
	a.ConfigInfo.Profiles = append(a.ConfigInfo.Profiles, importedProfiles...)

	// Save to file
	return a.UpdateProfiles(a.ConfigInfo.Profiles)
}

// Remote import/export functions
func (a *App) ExportRemotes() *dto.AppError {
	filePath, appErr := a.SaveFileDialog("Export Remotes", "remotes.json", []string{"*.json"})
	if appErr != nil {
		return appErr
	}

	if filePath == "" {
		// User cancelled the dialog
		return nil
	}

	// Get current remotes
	remotes := fsConfig.GetRemotes()

	// Convert to our models.Remotes format for export
	var exportRemotes models.Remotes
	for _, remote := range remotes {
		exportRemote := models.Remote{
			Name:  remote.Name,
			Type:  remote.Type,
			Token: make(map[string]interface{}),
		}

		// Add basic remote info to Token field for export
		// Note: We don't export sensitive authentication data
		exportRemote.Token["source"] = remote.Source
		exportRemote.Token["description"] = remote.Description

		exportRemotes = append(exportRemotes, exportRemote)
	}

	remotesJson, err := exportRemotes.ToJSON()
	if err != nil {
		return dto.NewAppError(err)
	}

	err = os.WriteFile(filePath, remotesJson, 0644)
	if err != nil {
		return dto.NewAppError(err)
	}

	return nil
}

func (a *App) ImportRemotes() *dto.AppError {
	filePath, appErr := a.OpenFileDialog("Import Remotes", []string{"*.json"})
	if appErr != nil {
		return appErr
	}

	if filePath == "" {
		// User cancelled the dialog
		return nil
	}

	// Read the file
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return dto.NewAppError(fmt.Errorf("failed to read file: %w", err))
	}

	// Parse the JSON
	var importedRemotes models.Remotes
	err = json.Unmarshal(fileData, &importedRemotes)
	if err != nil {
		return dto.NewAppError(fmt.Errorf("invalid JSON format: %w", err))
	}

	// Validate and create remotes
	for _, remote := range importedRemotes {
		if remote.Name == "" {
			return dto.NewAppError(fmt.Errorf("remote has no name"))
		}
		if remote.Type == "" {
			return dto.NewAppError(fmt.Errorf("remote '%s' has no type", remote.Name))
		}

		// Check if remote already exists
		existingRemotes := fsConfig.GetRemotes()
		for _, existing := range existingRemotes {
			if existing.Name == remote.Name {
				return dto.NewAppError(fmt.Errorf("remote '%s' already exists", remote.Name))
			}
		}

		// Create the remote (this will require user authentication for most cloud services)
		// Note: This creates the remote configuration but may require manual authentication
		ctx := context.Background()
		_, err := fsConfig.CreateRemote(ctx, remote.Name, remote.Type, rc.Params{}, fsConfig.UpdateRemoteOpt{})
		if err != nil {
			// If creation fails, clean up and return error
			fsConfig.DeleteRemote(remote.Name)
			return dto.NewAppError(fmt.Errorf("failed to create remote '%s': %w", remote.Name, err))
		}
	}

	return nil
}
